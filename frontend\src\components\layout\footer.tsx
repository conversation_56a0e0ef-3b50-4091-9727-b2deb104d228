// src/components/footer.tsx
'use client';

import Link from 'next/link';

export default function Footer() {
  return (
    <footer className="border-t bg-muted/40" aria-label="Site footer">
      <div className="container mx-auto py-8 px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <section aria-labelledby="footer-company-title">
            <h3 id="footer-company-title" className="text-lg font-semibold">Market O'Clock</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              Connecting suppliers and retailers with innovative marketplace solutions.
            </p>
          </section>

          {/* Marketplace Links */}
          <nav aria-label="Marketplace" className="text-sm">
            <h3 className="text-sm font-semibold">Marketplace</h3>
            <ul className="mt-2 space-y-2">
              <li>
                <Link href="/categories" className="text-muted-foreground hover:text-foreground">
                  Categories
                </Link>
              </li>
              <li>
                <Link href="/popular" className="text-muted-foreground hover:text-foreground">
                  Popular Items
                </Link>
              </li>
              <li>
                <Link href="/new" className="text-muted-foreground hover:text-foreground">
                  New Arrivals
                </Link>
              </li>
              <li>
                <Link href="/marketplace" className="text-muted-foreground hover:text-foreground">
                  Marketplace
                </Link>
              </li>
            </ul>
          </nav>

          {/* Company Links */}
          <nav aria-label="Company" className="text-sm">
            <h3 className="text-sm font-semibold">Company</h3>
            <ul className="mt-2 space-y-2">
              <li>
                <Link href="/about" className="text-muted-foreground hover:text-foreground">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-muted-foreground hover:text-foreground">
                  Contact
                </Link>
              </li>
              <li>
                <Link href="/careers" className="text-muted-foreground hover:text-foreground">
                  Careers
                </Link>
              </li>
              <li>
                <Link href="/blogs" className="text-muted-foreground hover:text-foreground">
                  Blogs
                </Link>
              </li>
            </ul>
          </nav>

          {/* Legal Links */}
          <nav aria-label="Legal" className="text-sm">
            <h3 className="text-sm font-semibold">Legal</h3>
            <ul className="mt-2 space-y-2">
              <li>
                <Link href="/terms" className="text-muted-foreground hover:text-foreground">
                  Terms of Service
                </Link>
              </li>
              <li>
                <Link href="/privacy" className="text-muted-foreground hover:text-foreground">
                  Privacy Policy
                </Link>
              </li>
            </ul>
          </nav>
        </div>

        {/* Contact and Copyright */}
        <div className="mt-8 pt-8 border-t text-center text-sm text-muted-foreground">
          <address className="not-italic mb-2"><EMAIL> | Nairobi, Kenya</address>
          <p>&copy; {new Date().getFullYear()} Market O'Clock. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
}